package com.kikitrade.activity.service.remote.impl;

import com.kikitrade.activity.api.RemoteTaskService;
import com.kikitrade.activity.api.model.*;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityTaskService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.action.ActivityEventAction;
import com.kikitrade.activity.service.task.domain.TaskCycleDomain;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class RemoteTaskServiceImpl implements RemoteTaskService {

    @Resource
    private ActivityTaskService activityTaskService;
    @Resource
    private ActivityEventAction activityEventAction;
    @Resource
    private TaskConfigService taskConfigService;
    @Resource
    private UploadOssUtil uploadOssUtil;

    /**
     * 任务列表
     * @param request
     * @return
     */
    @Override
    public List<TaskListResponse> taskList(TaskListRequest request) {
        if(StringUtils.isBlank(request.getSaasId())){
            return new ArrayList<>();
        }
        return activityTaskService.taskList(request);
    }

    /**
     * 任务详情
     * @param taskId
     * @return
     */
    @Override
    public TaskDetailResponse getTask(String taskId, String customerId) {
        return activityTaskService.findByTaskId(taskId, customerId, ActivityConstant.VipLevelEnum.NORMAL, "app");
    }

    /**
     * 根据 code 查询任务详情
     *
     * @param taskCode
     * @param customerId
     * @return
     */
    @Override
    public TaskCodeDetailResponse getTaskByCode(String saasId, String taskCode, String customerId) {
        return activityTaskService.findByTaskCode(saasId, taskCode, customerId);
    }

    @Override
    public List<Award> getTaskRewardByCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum){
        return activityTaskService.findByTaskCode(saasId, taskCode, vipLevelEnum);
    }

    @Override
    public List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum){
        return activityTaskService.listTaskRewards(saasId, taskCode, vipLevelEnum);
    }

    /**
     * 任务进度，暂时只支持组合任务的奖励的完成状态
     * @param saasId
     * @param taskId
     * @param customerId
     * @param type
     * @return
     */
    public TaskProgressResponse getTaskProgress(String saasId, String taskId, String customerId, String type){
        return activityTaskService.findProgressByTaskId(saasId, taskId, customerId, type);
    }

    @Override
    public TaskCompletedResult getTaskStatus(String saasId, String taskId, String customerId) {
        TaskConfigDTO taskConfig = taskConfigService.findByTaskIdAndWhiteFlag(taskId, customerId);
        if (Objects.isNull(taskConfig)) {
            log.warn("getTaskStatus taskConfig is null");
            return null;
        }
        String cycle = TaskCycleDomain.getCurrencyCycle(taskConfig, null);
        return activityTaskService.getTaskResult(customerId, cycle, taskId);
    }

    /**
     * 获取任务状态
     *
     * @param saasId
     * @param taskCode
     * @param customerId
     * @return
     */
    @Override
    public TaskCompletedResult getTaskStatusByCode(String saasId, String taskCode, String customerId) {
        List<TaskConfigDTO> taskConfigs = taskConfigService.findByTaskCodeAndWhiteFlag(saasId, taskCode, customerId);
        if (Objects.isNull(taskConfigs)) {
            log.warn("getTaskStatus taskConfig is null");
            return null;
        }
        TaskConfigDTO taskConfig = taskConfigs.get(0);
        String cycle = TaskCycleDomain.getCurrencyCycle(taskConfig, null);
        return activityTaskService.getTaskResult(customerId, cycle, taskConfig.getTaskId());
    }

    /**
     * 查询早鸟弹窗
     *
     * @param saasId
     * @param customerId
     * @return
     */
    @Override
    public TaskPopResponse getTaskEarlyBirdPop(String saasId, String customerId) {
        return activityTaskService.getTaskEarlyBirdPop(saasId, customerId);
    }

    /**
     * 做任务
     *
     * @param activityTaskDTO
     * @return
     */
    @Override
    public ActivityResponse<List<Award>> task(ActivityTaskDTO activityTaskDTO) {
        log.info("do task activityTaskDTO = {}", activityTaskDTO);
        log.info("user_track [task] start [{}, {}]", activityTaskDTO.getEventCode(), activityTaskDTO.getTaskId());
        ActivityEventMessage activityEventMessage = new ActivityEventMessage();
        activityEventMessage.setCustomerId(activityTaskDTO.getCustomerId());
        activityEventMessage.setEventCode(activityTaskDTO.getEventCode());
        activityEventMessage.setEventTime(activityTaskDTO.getEventTime());
        Map<String,Object> body = new HashMap<>();
        body.put("taskId", activityTaskDTO.getTaskId());
        body.put("saasId", activityTaskDTO.getSaasId());
        activityEventMessage.setBody(body);
        try{
            log.info("do action activityTaskDTO = {}", activityEventMessage);
            ActivityResponse<List<Award>> action = activityEventAction.action(activityEventMessage);
            log.info("user_track [task] end [{}, {}]", activityTaskDTO.getEventCode(), activityTaskDTO.getTaskId());
            return action;
        }catch (Exception ex){
            log.error("remoteTaskService task error:{}", activityTaskDTO, ex);
            return null;
        }
    }

    @Override
    public CheckInStatusDTO checkInStatus(String saasId, String customerId) {
        List<TaskConfigDTO> checkIns = taskConfigService.findByTaskCode(saasId, "check_in");
        if(CollectionUtils.isEmpty(checkIns)){
            return null;
        }
        checkIns.sort(Comparator.comparing(TaskConfigDTO::getOrder));
        CheckInStatusDTO checkInStatusDTO = new CheckInStatusDTO();
        for(TaskConfigDTO taskConfigDTO : checkIns){
            if(taskConfigDTO.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.add && taskConfigDTO.getCycle() != ActivityTaskConstant.TaskCycleEnum.once){
                if(checkInStatusDTO.getTitle() == null){
                    checkInStatusDTO.setTitle(taskConfigDTO.getTitle());
                    checkInStatusDTO.setDesc(taskConfigDTO.getDesc());
                    String code = taskConfigDTO.getShowCode() == null ? taskConfigDTO.getCode() : taskConfigDTO.getShowCode();
                    checkInStatusDTO.setIcon(uploadOssUtil.getLocation("quests/" + taskConfigDTO.getSaasId(), code+"."+ SaasConfigLoader.getConfig(taskConfigDTO.getSaasId()).getIconSuffix()));
                }
                //非连续任务
                TaskCompletedResult taskResult = activityTaskService.getTaskResult(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
                if(taskResult != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(taskResult.getStatus()) && !checkInStatusDTO.getCheckIn()){
                    checkInStatusDTO.setCheckIn(true);
                }
                checkInStatusDTO.setTodayReward(taskConfigDTO.getReward().get("0").get(0).getAmount());
                checkInStatusDTO.setTodayRewardType(taskConfigDTO.getReward().get("0").get(0).getType());
            } else if (taskConfigDTO.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series) {
                if(checkInStatusDTO.getTitle() != null){
                    checkInStatusDTO.setTitle(taskConfigDTO.getTitle());
                    checkInStatusDTO.setDesc(taskConfigDTO.getDesc());
                    String code = taskConfigDTO.getShowCode() == null ? taskConfigDTO.getCode() : taskConfigDTO.getShowCode();
                    checkInStatusDTO.setIcon(uploadOssUtil.getLocation("quests/" + taskConfigDTO.getSaasId(), code+"."+ SaasConfigLoader.getConfig(taskConfigDTO.getSaasId()).getIconSuffix()));
                }
                TaskCompletedResult taskResult = activityTaskService.getTaskResult(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
                TaskCompletedResult todayDetailResult = activityTaskService.getTaskDetailResult(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId(), TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDD));
                TaskCompletedResult yesterdayDetailResult = activityTaskService.getTaskDetailResult(customerId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId(), TimeUtil.getUtcTime(TimeUtil.addDay(new Date(), -1) ,TimeUtil.YYYYMMDD));
                if(todayDetailResult != null && ActivityConstant.TaskStatusEnum.DONE.name().equals(todayDetailResult.getStatus()) && !checkInStatusDTO.getCheckIn()){
                    checkInStatusDTO.setCheckIn(true);
                }
                if(taskResult != null){
                    if(taskResult.getProgress() < taskConfigDTO.getRewardFrequency()){
                        checkInStatusDTO.setCheckInDays(taskResult.getProgress());
                    } else if (taskResult.getProgress() % taskConfigDTO.getRewardFrequency() == 0) {
                        checkInStatusDTO.setCheckInDays(taskConfigDTO.getRewardFrequency());
                        //跨周时，如果今天未签到，则设置连续天数为0
                        if(!checkInStatusDTO.getCheckIn()){
                            checkInStatusDTO.setCheckInDays(0);
                        }
                    } else {
                        checkInStatusDTO.setCheckInDays(taskResult.getProgress() % taskConfigDTO.getRewardFrequency());
                    }
                }
                //如果昨天未签到，今天也未签到，则设置连续天数为0
                if((yesterdayDetailResult == null || !yesterdayDetailResult.isDone())
                        && (todayDetailResult == null || !todayDetailResult.isDone())){
                    checkInStatusDTO.setCheckInDays(0);
                }
                TaskConfigDTO configDTO = taskConfigService.findByTaskId(taskConfigDTO.getTaskId());
                Map<String, List<Award>> reward = configDTO.getReward();
                List<String> rewardKeys = reward.keySet().stream().sorted().collect(Collectors.toList());
                List<AwardDTO> awardDTOList = new ArrayList<>();
                for(String key : rewardKeys){
                    List<Award> awards = reward.get(key);
                    for (Award award : awards){
                        AwardDTO awardDTO = new AwardDTO();
                        awardDTO.setType(award.getType());
                        awardDTO.setCurrency(award.getCurrency());
                        awardDTO.setAmount(StringUtils.isNotBlank(award.getShowAmount()) ? award.getShowAmount() : award.getAmount());
                        awardDTO.setIndex(award.getIndex());
                        awardDTOList.add(awardDTO);
                    }
                }
                checkInStatusDTO.setCheckInReward(awardDTOList);
            }
        }

        return checkInStatusDTO;
    }

    @Override
    public TaskCompletedResult getTaskStatusByTargetId(String saasId, String taskId, String targetId) {
        TaskConfigDTO taskConfigDTO = taskConfigService.findByTaskId(taskId);
        return activityTaskService.getTaskResultBySocial(null, targetId, TaskCycleDomain.getCurrencyCycle(taskConfigDTO, null), taskConfigDTO.getTaskId());
    }
}
