package com.kikitrade.activity.service.task.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.api.model.TaskConfigDTO;
import com.kikitrade.activity.dal.tablestore.builder.ActivityTaskItemBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityTaskItem;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.constant.BusinessMonitorConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.util.TimeFormat;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.config.SaasConfigLoader;
import com.kikitrade.activity.service.config.TaskCodeConfig;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.mq.ActivityEventMessage;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.activity.service.task.ActivityTaskTccService;
import com.kikitrade.activity.service.task.TaskConfigService;
import com.kikitrade.activity.service.task.domain.TaskAwardDomain;
import com.kikitrade.activity.service.task.domain.TaskProgressDomain;
import com.kikitrade.activity.service.task.domain.TaskTargetDomain;
import com.kikitrade.asset.api.RemoteAssetService;
import com.kikitrade.asset.api.RemoteLimitedTimeAssetService;
import com.kikitrade.asset.model.AssetDTO;
import com.kikitrade.asset.model.constant.AssetBusinessType;
import com.kikitrade.asset.model.constant.AssetCategory;
import com.kikitrade.asset.model.constant.AssetType;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.QuestsPointRankingSnapshotDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static com.kikitrade.activity.model.util.TimeUtil.YYYYMMDD;

@Service
@Slf4j
public class ActivityTaskTccServiceImpl implements ActivityTaskTccService {
    @Resource
    private ActivityTaskItemBuilder activityTaskItemBuilder;
    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;
    @Resource
    private TaskConfigService taskConfigService;
    @Autowired
    private KiKiMonitor kiKiMonitor;
    @DubboReference
    private RemoteLimitedTimeAssetService remoteLimitedTimeAssetService;
    @DubboReference
    private RemoteAssetService remoteAssetService;
    @DubboReference
    private RemoteMemberService remoteMemberService;

    private static final String POINT_MULTIPLIER = "pointMultiplier";

    private static final String OVERWRITE_PROGRESS_FLAG = "overwriteProgress";

    /**
     * 做任务
     * @param activityEventMessage
     * @param taskConfig
     * @param codeConfig
     */
    @Override
    public List<Award> doTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, TaskCodeConfig codeConfig) throws Exception {
        log.info("Starting doTask for customerId: {}, taskId: {}", activityEventMessage.getCustomerId(), taskConfig.getTaskId());

        ActivityTaskItem partition = new ActivityTaskItem();
        partition.setCustomerId(activityEventMessage.getCustomerId());
        String vipLevel = Optional.ofNullable(activityEventMessage.getBody().get("vipLevel"))
                .map(Object::toString)
                .orElse(ActivityConstant.VipLevelEnum.NORMAL.name());
        AtomicLong progress = new AtomicLong(-1);

        Boolean execute = activityTaskItemBuilder.execute(partition, () -> {
            String targetId = TaskTargetDomain.getTargetId(activityEventMessage, codeConfig, taskConfig);
            boolean success = (codeConfig.getInc() < 0)
                    ? activityTaskItemBuilder.deleteDetail(activityEventMessage.getCustomerId(), taskConfig.getTaskId(), taskItem.getCycle(), targetId)
                    : insertTaskDetail(activityEventMessage, taskItem, taskConfig, targetId, codeConfig);

            if (success) {
                updateTaskProgress(taskItem, activityEventMessage, taskConfig, progress, vipLevel);
            }
            log.info("Task execution result: {}", success);
            return success;
        });

        if (progress.get() >= taskConfig.getLimit(vipLevel)) {
            completeTask(taskItem, activityEventMessage);
        }

        if (execute && progress.get() != -1) {
            List<Award> rewards = reward(progress, taskItem, taskConfig, activityEventMessage.getBody());
            log.info("Task completed with rewards: {}", rewards);
            return rewards;
        }
        log.info("Task did not complete successfully.");
        return null;
    }

    private boolean insertTaskDetail(ActivityEventMessage activityEventMessage, ActivityTaskItem taskItem, TaskConfigDTO taskConfig, String targetId, TaskCodeConfig codeConfig) {
        ActivityTaskItem detail = ActivityTaskItem.buildDetailPrimary(activityEventMessage.getCustomerId(), taskConfig.getTaskId(), taskItem.getCycle(), targetId);
        detail.setBusinessId(taskItem.getBusinessId());
        detail.setEvent(codeConfig.getCode());
        detail.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
        detail.setCompleteTime(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        detail.setCreated(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        detail.setModified(TimeUtil.getCurrentUtcTime(TimeUtil.YYYYMMDDHHMMSS));
        boolean result = activityTaskItemBuilder.insert(detail);
        log.info("Inserted task detail: {}, result: {}", detail, result);
        return result;
    }

    private void updateTaskProgress(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage, TaskConfigDTO taskConfig, AtomicLong progress, String vipLevel) {
        ActivityTaskItem primary = ActivityTaskItem.buildPrimary(taskItem.getCustomerId(), taskItem.getTaskId(), taskItem.getCycle());
        // 将原始进度返回，若后续需要可用于参与奖励计算
        activityEventMessage.getBody().put("previous_progress", taskItem.getProgress());
        boolean overwriteProgressFlag = Boolean.parseBoolean(String.valueOf(activityEventMessage.getBody().get(OVERWRITE_PROGRESS_FLAG)));
        if (overwriteProgressFlag && Objects.nonNull(activityEventMessage.getInc()) ) {
            activityEventMessage.setInc(activityEventMessage.getInc() - taskItem.getProgress());
            if (activityEventMessage.getInc() < 0) {
                return;
            }
        }
        Long incrementProgress = activityTaskItemBuilder.incrementProgress(primary, activityEventMessage.getInc());
        if(taskConfig.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series){
            ActivityTaskItem yesterdayTaskItem = activityTaskItemBuilder.findDetailByCustomer(taskItem.getCustomerId(), taskItem.getCycle(), taskItem.getTaskId(), TimeUtil.getDataStr(TimeUtil.addDay(new Date(), -1), TimeFormat.YYYYMMDD_PATTERN));
            progress.set(TaskProgressDomain.getProgress(taskConfig.getProgressType(), yesterdayTaskItem, Math.min(incrementProgress, taskConfig.getLimit(vipLevel))));
        }else{
            progress.set(TaskProgressDomain.getProgress(taskConfig.getProgressType(), null, Math.min(incrementProgress, taskConfig.getLimit(vipLevel))));
        }
        primary.setProgress(Math.min(progress.intValue(), Math.min(incrementProgress.intValue(), taskConfig.getLimit(vipLevel))));
        if(primary.getProgress() >= taskConfig.getLimit(vipLevel)){
            taskItem.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
            taskItem.setCompleteTime(TimeUtil.getUtcTime(TimeUtil.parseUnittime(activityEventMessage.getEventTime()), TimeUtil.YYYYMMDDHHMMSS));
            activityTaskItemBuilder.update(primary);
        }
        if(primary.getProgress() != incrementProgress.intValue()){
            activityTaskItemBuilder.update(primary);
        }
        log.info("Updated task progress: {}, incrementProgress: {}, progress: {}", primary, incrementProgress, progress);
    }

    private void completeTask(ActivityTaskItem taskItem, ActivityEventMessage activityEventMessage) {
        taskItem.setStatus(ActivityConstant.TaskStatusEnum.DONE.name());
        taskItem.setCompleteTime(TimeUtil.getUtcTime(TimeUtil.parseUnittime(activityEventMessage.getEventTime()), TimeUtil.YYYYMMDDHHMMSS));
        activityTaskItemBuilder.updateStatus(taskItem);
        log.info("Task completed: {}", taskItem);
    }

    private List<Award> reward(AtomicLong progress, ActivityTaskItem taskItem, TaskConfigDTO taskConfig, Map<String, Object> param) throws Exception{
        //每完成一个周期就发奖，一共能完成 completeTimes 次，
        if(taskConfig.getProvideType() == ActivityTaskConstant.ProvideType.auto
            || taskConfig.getProvideType() == ActivityTaskConstant.ProvideType.claim_reward
            || taskConfig.getProvideType() == ActivityTaskConstant.ProvideType.claim_reward_confirm){
            return calAndReward(progress, taskItem, taskConfig, param);
        }
        return null;
    }
    @Override
    public List<Award> calAndReward(AtomicLong progress, ActivityTaskItem taskItem, TaskConfigDTO taskConfig, Map<String, Object> param) throws Exception {

        log.info("Starting calAndReward for customerId: {}, taskId: {}", taskItem.getCustomerId(), taskConfig.getTaskId());
        List<Award> awards = TaskAwardDomain.getAward(taskConfig, progress, getVipLevel(param));
        log.info("[task] eventAction get awards: {}", awards);
        if (awards == null) {
            return null;
        }

        List<Award> resultAwards = new ArrayList<>();
        for (Award award : awards) {
            ActivityCustomReward reward = createReward(taskConfig, taskItem, progress, award, param);
            if (reward == null) {
                log.warn("Reward creation failed for award: {}", award);
                continue;
            }

            LauncherParameter launcherParameter = new LauncherParameter();
            launcherParameter.setActivityCustomReward(reward);
            launcherParameter.setProvideType(taskConfig.getProvideType());

            try {
                log.info("[task] eventAction send reward: {}", launcherParameter);
                monitorReward(taskConfig);
                activityRealTimeRewardTccService.reward(launcherParameter);
                log.info("user_track[reward] [{},{}],[{}],[{}]", taskConfig.getCode(), taskConfig.getTaskId(), award.getAmount(), award.getCurrency());

                award.setAmount(reward.getAmount());
                resultAwards.add(award);
            } catch (Exception e) {
                log.error("Reward error: {}", launcherParameter, e);
                throw e;
            }
        }
        log.info("calAndReward completed for customerId: {}, taskId: {}", taskItem.getCustomerId(), taskConfig.getTaskId());
        return resultAwards;
    }

    private String getVipLevel(Map<String, Object> param) {
        String vipLevel = param.get("vipLevel") == null ? null : String.valueOf(param.get("vipLevel"));
        log.info("Retrieved VIP level: {}", vipLevel);
        return vipLevel;
    }

    private ActivityCustomReward createReward(TaskConfigDTO taskConfig, ActivityTaskItem taskItem, AtomicLong progress, Award award, Map<String, Object> param) {
        log.info("Creating reward for customerId: {}, award: {}", taskItem.getCustomerId(), award);
        ActivityCustomReward reward = new ActivityCustomReward();
        reward.setSaasId(taskConfig.getSaasId());
        reward.setBatchId(taskItem.getCycle());
        reward.setBusinessId(generateBusinessId(taskConfig, taskItem, progress));
        reward.setCustomerId(taskItem.getCustomerId());
        reward.setSeq(generateSeq(taskItem, reward, award));
        reward.setAddress(param.get("address") == null ? null : String.valueOf(param.get("address")));
        reward.setInviteeId(param.get("inviteeId") == null ? null : String.valueOf(param.get("inviteeId")));

        param.put("_progress", progress);
        fillInterParams(award.getType(), award.getAmount(), param, taskConfig.getSaasId(), taskItem.getCustomerId());
        log.info("[task] eventAction get origin amount: [{}], param={}", award.getAmount(), JSON.toJSONString(param));
        String amount = award.getAmount(award.getAmount(), param);
        log.info("[task] eventAction get amount: {}", amount);

        if (amount == null || isNegativeAmount(taskConfig, amount)) {
            log.warn("Amount is null or negative for customerId: {}, amount: {}", taskItem.getCustomerId(), amount);
            return null;
        }

        amount = applyMultipliers(taskConfig, amount, param);
        reward.setAmount(amount);
        reward.setRewardType(award.getType());
        reward.setCurrency(award.getCurrency());
        reward.setBusinessType(getBusinessType(taskConfig));

        reward.addExtendParam("desc", getLedgerTitle(taskConfig));
        handleSpecialRewardTypes(taskConfig, taskItem, reward, param);

        log.info("Reward created: {}", reward);
        return reward;
    }

    private void monitorReward(TaskConfigDTO taskConfig) {
        if (taskConfig.getSaasId() != null && taskConfig.getCode() != null) {
            log.info("Monitoring reward for saasId: {}, code: {}", taskConfig.getSaasId(), taskConfig.getCode());
            kiKiMonitor.monitor(BusinessMonitorConstant.EVENT, new String[]{"saasId", taskConfig.getSaasId(), "code", taskConfig.getCode(), "stage", "reward"});
        }
    }

    private String generateBusinessId(TaskConfigDTO taskConfig, ActivityTaskItem taskItem, AtomicLong progress) {
        if (taskConfig.getProgressType() == ActivityTaskConstant.ProgressTypeEnum.series) {
            return taskItem.getBusinessId() + TimeUtil.getCurrentUtcTime(YYYYMMDD) + progress.get() / taskConfig.getRewardFrequency();
        } else {
            return taskItem.getBusinessId() + progress.get() / taskConfig.getRewardFrequency();
        }
    }

    private String generateSeq(ActivityTaskItem taskItem, ActivityCustomReward reward, Award award) {
        return taskItem.getEvent() + ":" + reward.getBusinessId() + ":" + award.getType();
    }

    private boolean isNegativeAmount(TaskConfigDTO taskConfig, String amount) {
        return !SaasConfigLoader.getConfig(taskConfig.getSaasId()).getAllowNegativeAsset() && BigDecimal.ZERO.compareTo(new BigDecimal(amount)) > 0;
    }

    private String applyMultipliers(TaskConfigDTO taskConfig, String amount, Map<String, Object> param) {
        if (param.containsKey(POINT_MULTIPLIER) && param.get(POINT_MULTIPLIER) != null && NumberUtils.isNumber(String.valueOf(param.get(POINT_MULTIPLIER)))) {
            amount = new BigDecimal(amount).multiply(new BigDecimal(String.valueOf(param.get(POINT_MULTIPLIER)))).setScale(1, RoundingMode.HALF_DOWN).toString();
        }
        return amount;
    }

    private String getBusinessType(TaskConfigDTO taskConfig) {
        return AssetBusinessType.valueOfByCodeDesc(taskConfig.getCode()) == null ? AssetBusinessType.ACTIVITY_TASK.getCodeDesc() : AssetBusinessType.valueOfByCodeDesc(taskConfig.getCode()).getCodeDesc();
    }

    private String getLedgerTitle(TaskConfigDTO taskConfig) {
        return taskConfig.getLedgerTitle() == null ? taskConfig.getTitle() : taskConfig.getLedgerTitle();
    }

    private void handleSpecialRewardTypes(TaskConfigDTO taskConfig, ActivityTaskItem taskItem, ActivityCustomReward reward, Map<String, Object> param) {
        if (ActivityConstant.AwardTypeEnum.BADGE.name().equals(reward.getRewardType())) {
            TaskConfigDTO superTask = taskConfigService.findByTaskIdAndWhiteFlag(taskConfig.getGroupId(), taskItem.getCustomerId());
            if (Objects.isNull(superTask)) {
                log.warn("calAndReward taskConfigDTO is null");
                return;
            }
            reward.addExtendParam("receiveEndTime", String.valueOf(superTask.getEndTime()));
            reward.setCustomerId(reward.getCustomerId());
        } else if (ActivityConstant.AwardTypeEnum.EXTRA_BOOST.name().equals(reward.getRewardType())) {
            reward.addExtendParam("receiveEndTime", String.valueOf(param.get("expireTime")));
        }
    }

    /**
     * 添加内部参数
     * _boost 基础加成
     * _extra_boost 额外加成
     * @param amountElStr
     * @param param
     */
    private void fillInterParams(String assetType, String amountElStr, Map<String, Object> param, String saasId, String customerId) {
        if (amountElStr.contains("_boost")) {
            AssetDTO boostAsset = remoteAssetService.asset(saasId, customerId, AssetType.BOOST, AssetCategory.NORMAL);
            param.put("_boost", Objects.nonNull(boostAsset) ? boostAsset.getAvailable() : BigDecimal.ZERO);
        }
        if (amountElStr.contains("_extra_boost")) {
            BigDecimal extraBoost = remoteLimitedTimeAssetService.sumExtraBoostByCustomerId(saasId, customerId);
            param.put("_extra_boost", Objects.nonNull(extraBoost) ? extraBoost : BigDecimal.ZERO);
        }
        DayOfWeek today = LocalDate.now().getDayOfWeek();
        for (DayOfWeek day : DayOfWeek.values()) {
            String key = "_is" + day.name().charAt(0) + day.name().substring(1).toLowerCase();
            if (amountElStr.contains(key)) {
                param.put(key, today == day ? 1 : 0);
            }
        }
        if (amountElStr.contains("_cyclePoints") || amountElStr.contains("_lastLadderLevel") || amountElStr.contains("_lastLadderSize") || amountElStr.contains("_yesterdayRank")) {
            QuestsPointRankingSnapshotDTO snapshotDTO = remoteMemberService.queryByCustomerIdAndDate(saasId, customerId, LocalDate.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            if (Objects.nonNull(snapshotDTO)) {
                Integer groupSize = remoteMemberService.queryGroupSize(saasId, snapshotDTO.getSeason(), snapshotDTO.getCycle(), snapshotDTO.getGroup(), snapshotDTO.getLevel(), LocalDate.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
                param.put("_cyclePoints", snapshotDTO.getPoint());
                param.put("_lastLadderLevel", snapshotDTO.getLevel());
                param.put("_lastLadderSize", groupSize);
                param.put("_yesterdayRank", snapshotDTO.getRank());
            }
        }
    }
}
