package com.kikitrade.activity.service.draw.impl;

import com.kikitrade.activity.dal.tablestore.builder.GiftPackConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ProgressChestConfigBuilder;
import com.kikitrade.activity.dal.tablestore.builder.RandomRewardPoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.GiftPackConfig;
import com.kikitrade.activity.dal.tablestore.model.ProgressChestConfig;
import com.kikitrade.activity.dal.tablestore.model.RandomRewardPool;
import com.kikitrade.activity.service.draw.RewardIssueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 奖励发放服务实现
 * 严格按照技术规格书要求实现动态奖励组合逻辑
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class RewardIssueServiceImpl implements RewardIssueService {
    
    @Resource
    private GiftPackConfigBuilder giftPackConfigBuilder;

    @Resource
    private ProgressChestConfigBuilder progressChestConfigBuilder;
    
    @Resource
    private RandomRewardPoolBuilder randomRewardPoolBuilder;
    
    // 规则类型常量
    private static final String RULE_TYPE_FIXED_ITEM = "FIXED_ITEM";
    private static final String RULE_TYPE_RANDOM_POOL_PICK = "RANDOM_POOL_PICK";
    
    @Override
    public List<IssuedReward> issueRewardsByPackId(String packId, String saasId) {
        log.info("开始发放礼包奖励: packId={}, saasId={}", packId, saasId);
        
        List<IssuedReward> issuedRewards = new ArrayList<>();
        
        try {
            // 1. 根据礼包ID从 gift_pack_config 表中查询礼包的基础配置信息
            List<GiftPackConfig> giftPackConfigs = giftPackConfigBuilder.findByPackId(packId, saasId);
            
            if (CollectionUtils.isEmpty(giftPackConfigs)) {
                log.warn("未找到礼包配置: packId={}, saasId={}", packId, saasId);
                return issuedRewards;
            }
            
            log.info("找到礼包配置规则数量: {}", giftPackConfigs.size());
            
            // 2. 遍历每个配置规则，根据规则类型处理
            for (GiftPackConfig config : giftPackConfigs) {
                try {
                    if (RULE_TYPE_FIXED_ITEM.equals(config.getRuleType())) {
                        // 处理固定物品
                        IssuedReward reward = processFixedItem(config);
                        if (reward != null) {
                            issuedRewards.add(reward);
                            log.info("发放固定物品: {}", reward);
                        }
                        
                    } else if (RULE_TYPE_RANDOM_POOL_PICK.equals(config.getRuleType())) {
                        // 处理随机池抽取
                        List<IssuedReward> randomRewards = pickFromRandomPool(
                                config.getRandomPoolId(), config.getPickCount(), saasId);
                        issuedRewards.addAll(randomRewards);
                        log.info("从随机池抽取奖励数量: {}, poolId={}", randomRewards.size(), config.getRandomPoolId());
                        
                    } else {
                        log.warn("未知的规则类型: {}, packId={}", config.getRuleType(), packId);
                    }
                    
                } catch (Exception e) {
                    log.error("处理礼包配置规则异常: configId={}, ruleType={}", 
                            config.getId(), config.getRuleType(), e);
                    // 单个规则失败不影响其他规则的处理
                }
            }
            
            log.info("礼包奖励发放完成: packId={}, 总奖励数量={}", packId, issuedRewards.size());
            
        } catch (Exception e) {
            log.error("发放礼包奖励异常: packId={}, saasId={}", packId, saasId, e);
        }
        
        return issuedRewards;
    }

    @Override
    public List<IssuedReward> issueRewardsByChestId(String chestId, String saasId) {
        log.info("开始发放宝箱奖励: chestId={}, saasId={}", chestId, saasId);

        List<IssuedReward> issuedRewards = new ArrayList<>();

        try {
            // 1. 根据宝箱ID从 chest_config 表中查询宝箱的基础配置信息
            ProgressChestConfig chestConfig = progressChestConfigBuilder.findByChestId(chestId, saasId);

            if (chestConfig == null) {
                log.warn("未找到宝箱配置: chestId={}, saasId={}", chestId, saasId);
                return issuedRewards;
            }

            return issueRewardsByPackId(chestConfig.getPackIdOnUnlock(), saasId);
        } catch (Exception e) {
            log.error("发放宝箱奖励异常: chestId={}, saasId={}", chestId, saasId, e);
        }

        return issuedRewards;
    }
    
    @Override
    public List<IssuedReward> pickFromRandomPool(String poolId, Integer pickCount, String saasId) {
        log.info("从随机池抽取奖励: poolId={}, pickCount={}, saasId={}", poolId, pickCount, saasId);
        
        List<IssuedReward> pickedRewards = new ArrayList<>();
        
        try {
            // 1. 获取奖励池关联的所有奖励配置
            List<RandomRewardPool> rewardPools = randomRewardPoolBuilder.findByPoolId(poolId, saasId);
            
            if (CollectionUtils.isEmpty(rewardPools)) {
                log.warn("随机奖励池为空: poolId={}, saasId={}", poolId, saasId);
                return pickedRewards;
            }
            
            log.info("随机奖励池物品数量: {}", rewardPools.size());
            
            // 2. 根据奖励池的随机算法和权重配置，动态组合生成最终的奖励内容
            List<RandomRewardPool> selectedPools = selectByWeight(rewardPools, pickCount);
            
            // 3. 生成最终奖励
            for (RandomRewardPool pool : selectedPools) {
                IssuedReward reward = createRewardFromPool(pool);
                if (reward != null) {
                    pickedRewards.add(reward);
                    log.info("抽取到奖励: {}", reward);
                }
            }
            
            log.info("随机池抽取完成: poolId={}, 实际获得奖励数量={}", poolId, pickedRewards.size());
            
        } catch (Exception e) {
            log.error("从随机池抽取奖励异常: poolId={}, pickCount={}", poolId, pickCount, e);
        }
        
        return pickedRewards;
    }
    
    @Override
    public IssuedReward processFixedItem(GiftPackConfig giftPackConfig) {
        try {
            if (giftPackConfig.getItemId() == null) {
                log.warn("固定物品配置缺少物品ID: configId={}", giftPackConfig.getId());
                return null;
            }
            
            // 计算随机数量（在最小值和最大值之间）
            Integer quantity = calculateRandomQuantity(
                    giftPackConfig.getQuantityMin(), giftPackConfig.getQuantityMax());
            
            // 创建奖励对象
            IssuedReward reward = new IssuedReward();
            reward.setItemId(giftPackConfig.getItemId());
            reward.setItemType("FIXED"); // 固定物品类型
            reward.setItemName("固定物品_" + giftPackConfig.getItemId()); // 实际应该从物品表查询
            reward.setQuantity(quantity);
            reward.setDescription("固定发放的物品");
            
            return reward;
            
        } catch (Exception e) {
            log.error("处理固定物品异常: configId={}", giftPackConfig.getId(), e);
            return null;
        }
    }
    
    @Override
    public List<RandomRewardPool> selectByWeight(List<RandomRewardPool> rewardPools, int count) {
        if (CollectionUtils.isEmpty(rewardPools) || count <= 0) {
            return new ArrayList<>();
        }
        
        List<RandomRewardPool> selected = new ArrayList<>();
        List<RandomRewardPool> availablePools = new ArrayList<>(rewardPools);
        
        try {
            // 计算总权重
            int totalWeight = availablePools.stream()
                    .mapToInt(pool -> pool.getWeight() != null ? pool.getWeight() : 1)
                    .sum();
            
            log.debug("权重随机选择: 总权重={}, 选择数量={}", totalWeight, count);
            
            // 按权重随机选择
            for (int i = 0; i < count && !availablePools.isEmpty(); i++) {
                RandomRewardPool selectedPool = selectSingleByWeight(availablePools, totalWeight);
                if (selectedPool != null) {
                    selected.add(selectedPool);
                    
                    // 从可选池中移除已选择的物品（避免重复）
                    availablePools.remove(selectedPool);
                    totalWeight -= (selectedPool.getWeight() != null ? selectedPool.getWeight() : 1);
                    
                    log.debug("选中奖励: itemId={}, weight={}", selectedPool.getItemId(), selectedPool.getWeight());
                }
            }
            
        } catch (Exception e) {
            log.error("权重随机选择异常: poolSize={}, count={}", rewardPools.size(), count, e);
        }
        
        return selected;
    }
    
    /**
     * 按权重随机选择单个奖励
     */
    private RandomRewardPool selectSingleByWeight(List<RandomRewardPool> pools, int totalWeight) {
        if (totalWeight <= 0) {
            return pools.get(ThreadLocalRandom.current().nextInt(pools.size()));
        }
        
        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;
        
        for (RandomRewardPool pool : pools) {
            int weight = pool.getWeight() != null ? pool.getWeight() : 1;
            currentWeight += weight;
            
            if (randomValue < currentWeight) {
                return pool;
            }
        }
        
        // 降级方案：如果权重计算有问题，随机选择一个
        return pools.get(ThreadLocalRandom.current().nextInt(pools.size()));
    }
    
    /**
     * 从奖励池配置创建奖励对象
     */
    private IssuedReward createRewardFromPool(RandomRewardPool pool) {
        try {
            // 计算随机数量
            Integer quantity = calculateRandomQuantity(pool.getQuantityMin(), pool.getQuantityMax());
            
            return new IssuedReward(
                    pool.getItemId(),
                    pool.getItemType(),
                    pool.getItemName(),
                    quantity,
                    pool.getDescription(),
                    pool.getIconUrl()
            );
            
        } catch (Exception e) {
            log.error("创建奖励对象异常: poolId={}, itemId={}", pool.getPoolId(), pool.getItemId(), e);
            return null;
        }
    }
    
    /**
     * 计算随机数量（在最小值和最大值之间）
     */
    private Integer calculateRandomQuantity(Integer min, Integer max) {
        if (min == null) min = 1;
        if (max == null) max = min;
        
        if (min.equals(max)) {
            return min;
        }
        
        return ThreadLocalRandom.current().nextInt(min, max + 1);
    }
}
