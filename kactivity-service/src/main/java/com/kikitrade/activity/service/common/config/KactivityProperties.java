package com.kikitrade.activity.service.common.config;

import com.google.common.base.Splitter;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * Solid Config
 *
 * <AUTHOR>
 * @create 2021/9/6 11:44 下午
 * @modify
 */
@Configuration
@ConfigurationProperties
@Getter
public class KactivityProperties {

    @Value("${saas-id}")
    private String saasId;

    @Value("${batch.inteface-limiter}")
    private String limiter;

    @Value("${batch.reward-shard}")
    private Integer rewardShard;

    @Value("${batch.import.max-job-count}")
    private int maxJobCount;

    @Value("${batch.reward.retry-job-count}")
    private int retryJobCount;

    @Value("${reward.notice-switch}")
    private boolean noticeSwitch;

    @Value("${reward.statistics.currency:USD}")
    private String rewardStatisticsCurrency;

    // 新手邀请任务的开始时间戳
    @Value("${activity.invite.reward.start.timestamp:0}")
    private Long inviteStartTime;

    // 新手专享任务，自用户注册 x 天后对该用户过期，过期后任务列表不展示，后续完成不发奖
    @Value("${activity.novice.task.expire.days:15}")
    private Integer noviceTaskExpireDays;

    // 使用产品帐时，如果需要使用统一的币种进行转换，则使用该配置的币种，默认:USDC
    @Value("${product.account.currency:USDC}")
    private String productAccountCurrency;

    Splitter.MapSplitter  mapSplitter = Splitter.on(";").withKeyValueSeparator(":");

    public Map<String,String> getLimiter() {
        return mapSplitter.split(limiter);
    }

    @Value("${activity.http.pool.size:150}")
    private Integer httpPoolSize;
    @Value("${activity.nft.reward.point:50}")
    private Integer nftRewardPoint;
    @Value("${activity.nft.chain}")
    private String nftChain;

    @Value("${activity.nft.app.token:Y3F0X3JRYmRWNjRRV01INFRxUnJSYnRtS0RWUXk4ZHg6}")
    private String nftAppToken;

    @Value("${activity.quests.dingtalk.url}")
    private String questsDingTalkUrl;

    @Value("${task.white.date:5}")
    private Integer taskWhiteDate;

    @Value("${activity.clean.taskItem.interval.seconds:604800}")
    private Long cleanTaskItemIntervalSeconds;

    @Value("${activity.clean.taskItem.days:30}")
    private Integer cleanTaskItemDays;

    @Value("${activity.precision.metrics.tunnelId}")
    private String precisionMetricsTunnelId;

    @Value("${activity.verify.code.expire.minutes:5}")
    private Long verifyCodeExpireMinutes;

    @Value("${activity.twitter.api.key:e33e4933ec684e1eab43936cf95572d3}")
    private String twitterApiKey;

    @Value("${activity.quests.api.host}")
    private String questsApiHost;

    @Value("${activity.quests.app.key.suffix}")
    private String questsAppKeySuffix = "%s:%s:quests";
}
