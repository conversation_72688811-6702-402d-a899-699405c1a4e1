package com.kikitrade.activity.service.remote.impl;

import cn.hutool.core.bean.BeanUtil;
import com.dipbit.dtm.client.exception.DtmDomainException;
import com.kikitrade.activity.api.RemoteGoodsService;
import com.kikitrade.activity.api.exception.ActivityException;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.api.model.request.GoodsOrderRequest;
import com.kikitrade.activity.api.model.response.GoodsDetailResponse;
import com.kikitrade.activity.api.model.response.GoodsResponse;
import com.kikitrade.activity.dal.tablestore.model.Goods;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.common.UploadOssUtil;
import com.kikitrade.activity.service.goods.GoodsService;
import com.kikitrade.asset.model.exception.AssetException;
import com.kikitrade.framework.common.model.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/12/28 11:43
 */
@DubboService
@Slf4j
public class RemoteGoodsServiceImpl implements RemoteGoodsService {

    @Resource
    private GoodsService goodsService;

    @Override
    public List<GoodsResponse> goodsList(int offset, int limit, String saasId, String exclude) {
        log.info("goodsList request:{},{},{}", offset, limit, saasId);
        Page<Goods> page = goodsService.findAll(offset, limit, saasId, exclude);
        log.info("goodsList response:{}", page);
        if(page == null || CollectionUtils.isEmpty(page.getRows())){
            return null;
        }
        List<Goods> goodses = page.getRows();
        List<GoodsResponse> responses = new ArrayList<>();
        for(Goods goods : goodses){
            GoodsResponse goodsResponse = BeanUtil.copyProperties(goods, GoodsResponse.class);
            if(goods.getEndTime() < System.currentTimeMillis()){
                goodsResponse.setStatus(ActivityConstant.GoodsStatus.EXPIRE.getCode());
            }
            responses.add(goodsResponse);
        }
        return responses;
    }

    @Override
    public GoodsDetailResponse goods(String goodsId) {
        Goods goods = goodsService.findById(goodsId);
        GoodsDetailResponse goodsResponse = BeanUtil.copyProperties(goods, GoodsDetailResponse.class);
        Map<String,String> image = new HashMap<>();
        goodsResponse.setImageMap(image);
        if(goods.getEndTime() < System.currentTimeMillis()){
            goodsResponse.setStatus(ActivityConstant.GoodsStatus.EXPIRE.getCode());
        }
        return goodsResponse;
    }
}
