package com.kikitrade.activity.service.reward.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import com.alibaba.fastjson.JSON;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.common.config.RateLimiterConfig;
import com.kikitrade.activity.service.reward.CustomerService;
import com.kikitrade.activity.service.reward.model.CustomerMiscDTO;
import com.kikitrade.ksocial.api.model.dto.CustomerKolDTO;
import com.kikitrade.ksocial.api.service.RemoteSocialService;
import com.kikitrade.ksocial.common.constants.CustomerConstants;
import com.kikitrade.member.api.RemoteMemberService;
import com.kikitrade.member.model.request.MembershipRequest;
import com.kikitrade.member.model.response.MembershipResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CustomerServiceImpl implements CustomerService {

    @Resource
    private KactivityProperties kactivityProperties;

    @Override
    public CustomerCacheDTO getById(String customerId) {
        String saasId = kactivityProperties.getSaasId();
        String key = RedisKeyConst.getKey(RedisKeyConst.ACTIVITY_CUSTOMER_KEY.getPrefix(), saasId + customerId);
        try{

        }catch (Exception ex){
            log.error("customerService getById error:{}", customerId,ex);
        }
        return null;
    }
}
