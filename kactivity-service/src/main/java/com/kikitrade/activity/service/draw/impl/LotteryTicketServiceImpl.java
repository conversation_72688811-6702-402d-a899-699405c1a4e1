package com.kikitrade.activity.service.draw.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.*;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.kikitrade.activity.api.model.request.reward.ExchangeTicketsRequest;
import com.kikitrade.activity.api.model.response.reward.ExchangeTicketsResponse;
import com.kikitrade.activity.dal.tablestore.builder.PrizePoolBuilder;
import com.kikitrade.activity.dal.tablestore.model.PrizePool;
import com.kikitrade.activity.service.common.config.KactivityProperties;
import com.kikitrade.activity.service.draw.LotteryTicketService;
import com.kikitrade.activity.service.draw.preference.UserPreferenceService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 抽奖券管理服务实现
 * 基于UserLotteryProfile存储抽奖券信息，支持两阶段抽奖系统
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class LotteryTicketServiceImpl implements LotteryTicketService {
    
    @Resource
    private UserPreferenceService userPreferenceService;

    @Resource
    private PrizePoolBuilder prizePoolBuilder;

    @Resource
    private KactivityProperties kactivityProperties;

    @Override
    public ExchangeTicketsResponse exchangeTickets(ExchangeTicketsRequest request) {
        try {
            // 1. 参数验证
            if (!validateExchangeRequest(request)) {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("INVALID_PARAMS")
                        .message("请求参数无效")
                        .build();
            }
            ExchangeTicketsDTO exchangeTicketsDTO =
                ExchangeTicketsDTO.builder()
                    .userId(request.getCustomerId())
                    .saasId(request.getSaasId())
                    .prizePoolCode(request.getPrizePoolCode())
                    .exchangeType(request.getExchangeType())
                    .assetType(request.getAssetType())
                    .optionId(request.getOptionId())
                    .businessId(generateOrderId())
                    .businessType("ACTIVITY_LOTTERY")
                    .build();

            // 4. 计算抽奖券数量
            calculateTicketCount(exchangeTicketsDTO);

            // 5. 执行兑换操作
            boolean success = performExchange(exchangeTicketsDTO);
            if (success) {
                return ExchangeTicketsResponse.builder()
                        .success(true)
                        .ticketType("TICKET_" + exchangeTicketsDTO.getPrizePoolCode())
                        .ticketsObtained(exchangeTicketsDTO.getTicketCount())
                        .assetType(exchangeTicketsDTO.getAssetType())
                        .message("兑换成功")
                        .build();
            } else {
                return ExchangeTicketsResponse.builder()
                        .success(false)
                        .errorCode("EXCHANGE_FAILED")
                        .message("兑换失败")
                        .build();
            }
            
        } catch (Exception e) {
            log.error("兑换抽奖券异常: userId={}", request.getCustomerId(), e);
            return ExchangeTicketsResponse.builder()
                    .success(false)
                    .errorCode("SYSTEM_ERROR")
                    .message("系统异常")
                    .build();
        }
    }
    
    @Override
    public Integer getUserTicketBalance(String userId, String saasId, String ticketType) {
        return null;
    }

    @Override
    public boolean consumeTickets(String userId, String saasId, String ticketType, Integer count) {
        log.info("消费抽奖券: userId={}, ticketType={}, count={}", userId, ticketType, count);
        
        try {
            Integer currentBalance = getUserTicketBalance(userId, saasId, ticketType);
            if (currentBalance < count) {
                log.warn("抽奖券余额不足: userId={}, current={}, required={}", userId, currentBalance, count);
                return false;
            }
            
            Integer newBalance = currentBalance - count;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("消费抽奖券异常: userId={}, ticketType={}, count={}", userId, ticketType, count, e);
            return false;
        }
    }
    
    @Override
    public boolean refundTickets(String userId, String saasId, String ticketType, Integer count) {
        log.info("退还抽奖券: userId={}, ticketType={}, count={}", userId, ticketType, count);
        
        try {
            Integer currentBalance = getUserTicketBalance(userId, saasId, ticketType);
            Integer newBalance = currentBalance + count;
            String preferenceKey = "TICKET_BALANCE_" + ticketType;
            return userPreferenceService.setUserPreference(userId, saasId, preferenceKey, newBalance.toString());
            
        } catch (Exception e) {
            log.error("退还抽奖券异常: userId={}, ticketType={}, count={}", userId, ticketType, count, e);
            return false;
        }
    }
    
    /**
     * 验证兑换请求参数
     */
    private boolean validateExchangeRequest(ExchangeTicketsRequest request) {
        if (request == null || !StringUtils.hasText(request.getCustomerId()) || !StringUtils.hasText(request.getSaasId())) {
            return false;
        }
    }
    
    /**
     * 执行兑换操作，通过错误码来判断余额不足
     */
    private boolean performExchange(ExchangeTicketsDTO exchangeTicketsDTO) {
        try {
            //1. 扣除用户资产
            transfer(exchangeTicketsDTO);
            transferIn(exchangeTicketsDTO);

        } catch (Exception e) {
            log.error("执行兑换操作异常: userId={}", exchangeTicketsDTO.getUserId(), e);
            return false;
        }
    }

    private boolean transfer(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        String transferUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/transfer";
        HttpRequest httpRequest = HttpUtil.createGet(transferUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));

        Map<String, String> params = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getCost()));
        jsonObject.put("fromCustomerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("fromAssetType", exchangeTicketsDTO.getAssetType());
        jsonObject.put("businessId", exchangeTicketsDTO.getBusinessId());
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("toAssetType", "WAITING_TRANSFER");
        jsonObject.put("toCustomerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());
        httpRequest.body(HttpUtil.toParams(params), ContentType.FORM_URLENCODED.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK || org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            log.error("getTwitterFollow, getTaskProcessResult from quests fail, response: {}", JSON.toJSONString(response));
            return false;
        }
        return true;
    }

    private boolean transferIn(ExchangeTicketsDTO exchangeTicketsDTO) {
        //这里要构造http请求调用其他服务
        //2. 增加抽奖券
        String ticketName = "TICKET_" + exchangeTicketsDTO.getPrizePoolCode();
        String transferInUrl = kactivityProperties.getQuestsApiHost() + "/s2/member/transferin";
        HttpRequest httpRequest = HttpUtil.createGet(transferInUrl);
        httpRequest.addHeaders(buildHeaders(kactivityProperties.getSaasId()));
        Map<String, String> params = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("saasId", exchangeTicketsDTO.getSaasId());
        jsonObject.put("amount", String.valueOf(exchangeTicketsDTO.getCost()));
        jsonObject.put("customerId", exchangeTicketsDTO.getUserId());
        jsonObject.put("businessId", exchangeTicketsDTO.getBusinessId());
        jsonObject.put("businessType", exchangeTicketsDTO.getBusinessType());
        jsonObject.put("type", ticketName);
        jsonObject.put("desc", exchangeTicketsDTO.getDesc());
        httpRequest.body(HttpUtil.toParams(params), ContentType.FORM_URLENCODED.toString());

        HttpResponse response = httpRequest.execute();
        if (response == null || response.getStatus() != HttpStatus.HTTP_OK || org.apache.commons.lang3.StringUtils.isBlank(response.body())) {
            log.error("getTwitterFollow, getTaskProcessResult from quests fail, response: {}", JSON.toJSONString(response));
            return false;
        }
    }

    @NotNull
    private HashMap<String, String> buildHeaders(String saasId) {
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        long current = System.currentTimeMillis();
        String appKey = bCryptPasswordEncoder.encode(String.format(kactivityProperties.getQuestsAppKeySuffix(), saasId, current));
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Authorization", Base64Utils.encodeToString((current + ":" + appKey).getBytes()));
        headers.put("saas_id", saasId);
        return headers;
    }
    
    /**
     * 从奖池配置中计算抽奖券数量
     */
    private void calculateTicketCount(ExchangeTicketsDTO exchangeTicketsDTO) {
        log.info("计算抽奖券数量: exchangeTicketsDTO={}", JSON.toJSONString(exchangeTicketsDTO));
        try {
            // 1. 获取奖池配置
            PrizePool prizePool = prizePoolBuilder.findByCodeAndSaasId(exchangeTicketsDTO.getPrizePoolCode(), exchangeTicketsDTO.getSaasId());
            if (prizePool == null) {
                log.warn("奖池不存在: prizePoolCode={}, saasId={}", exchangeTicketsDTO.getPrizePoolCode(), exchangeTicketsDTO.getSaasId());
                return;
            }
            // 2. 解析兑换规则JSON
            List<ExchangeRule> exchangeRules = parseExchangeRules(prizePool.getExchangeRules());
            if (exchangeRules == null || exchangeRules.isEmpty()) {
                log.warn("奖池兑换规则为空: prizePoolCode={}", exchangeTicketsDTO.getPrizePoolCode());
                return;
            }
            // 3. 查找匹配的资产类型和兑换类型
            for (ExchangeRule rule : exchangeRules) {
                if (exchangeTicketsDTO.getAssetType().equals(rule.getAssetType()) && exchangeTicketsDTO.getExchangeType().equals(rule.getExchangeType())) {
                    // 从options中找到匹配的选项
                    if (rule.getOptions() != null && !rule.getOptions().isEmpty()) {
                        for (ExchangeOption option : rule.getOptions()) {
                            if (exchangeTicketsDTO.getOptionId().equals(option.getOptionId())) {
                                exchangeTicketsDTO.setCost(option.getCost());
                                exchangeTicketsDTO.setTicketCount(option.getTickets());
                                exchangeTicketsDTO.setDesc(option.getDescription());
                            }
                        }
                    }
                }
            }
            log.warn("未找到匹配的兑换规则");
        } catch (Exception e) {
            log.error("计算抽奖券数量异常", e);
        }
    }

    /**
     * 解析兑换规则JSON
     */
    private List<ExchangeRule> parseExchangeRules(String exchangeRulesJson) {
        if (!StringUtils.hasText(exchangeRulesJson)) {
            return null;
        }

        return JSON.parseObject(exchangeRulesJson, new TypeReference<List<Map<String, Object>>>(){});
    }

    /**
     * 生成兑换订单ID
     */
    private String generateOrderId() {
        return "EX_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 兑换规则内部类
     */
    @Data
    private static class ExchangeRule {
        private String exchangeType;
        private String assetType;
        private List<ExchangeOption> options;

        // 为了向后兼容，保留getCost方法
        public Integer getCost() {
            if (options != null && !options.isEmpty()) {
                return options.get(0).getCost();
            }
            return null;
        }
    }

    /**
     * 兑换选项内部类
     */
    @Data
    private static class ExchangeOption {
        private String optionId;
        private Integer cost;
        private Integer tickets;
        private String description;
    }

    @Data
    @Builder
    private static class ExchangeTicketsDTO {
        private String userId;
        private String saasId;
        private String prizePoolCode;
        private String exchangeType;
        private String assetType;
        private String optionId;
        private Integer cost;
        private Integer ticketCount;
        private String desc;
        private String businessId;
        private String businessType;

    }
}
