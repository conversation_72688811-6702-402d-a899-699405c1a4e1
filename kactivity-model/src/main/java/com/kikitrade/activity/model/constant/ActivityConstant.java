package com.kikitrade.activity.model.constant;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * description:
 *
 * @auther: dembin.deng
 * @date: 2018/7/11 17:07
 */
public class ActivityConstant {

    /**
     * 二步行为：
     * 1 跳过，什么也不做
     * 2 自动兑换
     * 3 奖励邀请者
     */
    public enum SecondStep {
        SKIP,
        AUTO_EXCHANGE,
        REWARD_INVITER;
    }

    public enum Type {
        GIFT(1, "gift", RuleEvent.REGISTER, true), // register
        REBATE(2, "rebate", RuleEvent.REBATE, false),
        RECHARGE_PARTITION(3, "recharge_partition", RuleEvent.RECHARGE_PARTITION, true),
        RECHARGE_RANDOM_REWARD(4, "recharge_random_reward", RuleEvent.DEPOSIT, false), // deposit
        RETURN_FEE(5, "return_fee", RuleEvent.RETURN_FEE, false),
        INTEREST_DISCOUNT(6, "interest_discount", RuleEvent.INTEREST_DISCOUNT, false),
        WITHDRAW_LIMIT_REWARD(7, "withdraw_limit_reward", RuleEvent.DEPOSIT, false), // deposit
        INVITE_GIFT(8, "invite_gift", RuleEvent.REGISTER, false), // register
        CURRENT_DISCOUNT(9, "current_discount", RuleEvent.CURRENT_DISCOUNT, false),
        BIND_MOBILE_GIFT(10, "bind_mobile_gift", RuleEvent.BIND_MOBILE, true), // bind_mobile
        INVITE_BIND_MOBILE_GIFT(11, "invite_bind_mobile_gift", RuleEvent.BIND_MOBILE, false), // bind_mobile
        DISCOUNT_BUY(12, "discount_buy", RuleEvent.DISCOUNT_BUY, false),
        REGISTER_TREASURE(13, "register_treasure", RuleEvent.REGISTER, false),// register
        INVITE_TREASURE(14, "invite_treasure", RuleEvent.DEPOSIT, false), // deposit
        LUCKY_BOX(15, "lucky_box", RuleEvent.LUCKY_BOX, false),
        INVITE_TRADE_REBATE(16, "invite_trade_rebate", RuleEvent.REBATE, false),
        KIKI_TRADE_REBATE(17, "kiki_rebate", RuleEvent.REBATE, false),
        KIKI_INVITE_GIFT(18, "legacy_kiki_invite_gift", RuleEvent.KIKI_REBATE, true),
        KIKI_FIAT_DEPOSIT_GIFT(19, "kiki_fiat_deposit_gift", RuleEvent.KIKI_FIAT_DEPOSIT, true),
        KIKI_SLOT_INVITE_GIFT(20, "kiki_slot_invite_gift", RuleEvent.REGISTER, true),
        KIKI_FIAT_DEPOSIT_SINGLE(21, "kiki_fiat_deposit_single", RuleEvent.KIKI_FIAT_DEPOSIT, false, SecondStep.AUTO_EXCHANGE),
        KIKI_FIAT_DEPOSIT_TOTAL(22, "kiki_fiat_deposit_total", RuleEvent.KIKI_FIAT_DEPOSIT_FINISH, true, SecondStep.AUTO_EXCHANGE),
        KIKI_INVITE_USER_DEPOSIT_SINGLE(23, "kiki_invite_fiat_deposit", RuleEvent.KIKI_FIAT_DEPOSIT, false, SecondStep.REWARD_INVITER);

        private int code;
        private String desc;
        private RuleEvent event;
        private boolean timesCheck;
        private SecondStep secondStep;

        Type(int code, String desc, RuleEvent event, boolean timesCheck) {
            this.code = code;
            this.desc = desc;
            this.event = event;
            this.timesCheck = timesCheck;
        }

        Type(int code, String desc, RuleEvent event, boolean timesCheck, SecondStep secondStep) {
            this.code = code;
            this.desc = desc;
            this.event = event;
            this.timesCheck = timesCheck;
            this.secondStep = secondStep;
        }

        public int getCode() {
            return code;
        }

        public SecondStep getSecondStep() {
            return secondStep;
        }

        public boolean needTimesCheck() {
            return timesCheck;
        }

        public RuleEvent getEvent() {
            return event;
        }

        public static Type getType(int code) {
            for (Type r : Type.values()) {
                if (r.getCode() == code) {
                    return r;
                }
            }
            return null;
        }
    }


    public enum RuleEvent {
        REGISTER(1, "register"),
        REBATE(2, "rebate"),
        RECHARGE_PARTITION(3, "recharge_partition"),
        DEPOSIT(4, "deposit"),
        RETURN_FEE(5, "return_fee"),
        INTEREST_DISCOUNT(6, "interest_discount"),
        CURRENT_DISCOUNT(9, "current_discount"),
        BIND_MOBILE(10, "bind_mobile"),
        DISCOUNT_BUY(12, "discount_buy"),
        LUCKY_BOX(13, "lucky_box"),
        KIKI_REBATE(18, "kiki_rebate"),
        KIKI_FIAT_DEPOSIT(19, "kiki_fiat_deposit"),
        KIKI_FIAT_DEPOSIT_FINISH(20, "kiki_fiat_deposit_finish");

        private int code;
        private String desc;

        RuleEvent(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public static RuleEvent fromCode(int code) {
            return Arrays.stream(RuleEvent.values()).filter(re -> re.code() == code).findFirst().orElse(null);
        }

    }


    public enum ExecuteType {
        //0-自动
        AUTO(0, "auto"),
        //1-手动
        MANUAL(1, "manual"),
        //2-定时
        TIMED(2, "timed");

        private int code;
        private String desc;

        ExecuteType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }
    }


    public enum RewardType {

        //Job 调度
        JOB_DISPATCH(0, "job_dispatch"),
        //1-手工发奖
        MANUAL_REWARD(1, "manual_reward"),
        //2 定时发奖
        TIMED_REWARD(2, "timed_reward"),
        //3 数据处理
        DATA_PROCESS(3, "data_process");

        private int code;
        private String desc;

        RewardType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public static RewardType getRewardType(int code) {
            for (RewardType r : RewardType.values()) {
                if (r.getCode() == code) {
                    return r;
                }
            }

            return null;
        }
    }


    public enum GiftType {
        EXCHANGE,
        NORMAL,
        FIAT;

        public static GiftType getByCode(int code) {
            return Arrays.stream(values()).filter(f -> f.ordinal() == code).findFirst().orElse(null);
        }
    }


    //活动状态
    //0-新建
    //1-已发布
    //2-进行中
    //3-暂停
    //4-已结束
    //5-失效（删除）

    public enum Status {
        //0-新建
        CREATED(0, "created"),
        //1-已发布
        PUBLISHED(1, "published"),
        //2-进行中
        PROCESSING(2, "processing"),
        //3-暂停
        PAUSE(3, "pause"),
        //4-已结束
        END(4, "end"),
        //5-失效
        INVALID(5, "invalid");

        private int code;
        private String desc;

        Status(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

    }


    //批次状态
    //0-新建
    //1-处理中
    //2-处理成功
    //3-处理失败

    public enum BatchSatus {
        //0-新建
        RECORDED(0, "recorded"),
        //1-处理中
        PROCESSING(1, "processing"),
        //2-处理成功
        SUCCEEDED(2, "succeeded"),
        //3-处理失败
        FAILED(3, "failed");

        private int code;
        private String desc;

        BatchSatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

    }


    public enum MsgStatus {
        //0-已登记（消息已登记）
        RECORDED(0, "recorded"),
        //1-已检查通过 (消息已处理，满足活动参加条件)
        CHECK_PASSED(1, "check_passed"),
        //2-未检查通过 (消息已处理，无权参加活动或无活动可参加)
        CHECK_FAILED(2, "check_failed"),
        //3-action 处理成功 (消息已处理，成功参加活动)
        ACTION_SUCCEEDED(3, "action_succeeded"),
        //4-action 处理失败 (消息已处理，参加活动失败，处理过程失败)
        ACTION_FAILED(4, "action_failed"),
        //5-skip_action(跳过发奖)
        SKIP_ACTION(5, "skip_action"),
        //9999-处理失败
        PROCESS_FAILED(9999, "");

        private int code;
        private String desc;

        MsgStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public String getCodeAndDesc() {
            return code + "-" + desc;
        }

        public String getParaMsg(String target) {
            return code + "-" + target;
        }

    }

    //状态
    // 0-已登记
    // 1-处理中
    // 2-已完成
    // 3-已失败
    public enum RecordStatus {

        //0-已登记
        RECORDED(0, "recorded"),
        //1-处理中
        PROCESSING(1, "processing"),
        //2-已完成
        COMPLETED(2, "completed"),
        //3-已失败
        FAILED(3, "failed");


        private int code;
        private String desc;

        RecordStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }


    public enum FiatRewardType {
        //0-单笔入金
        SINGEL(0),
        //1-累计入金
        TOTAL(1);

        FiatRewardType(int code) {
            this.code = code;
        }

        private int code;

        public int getCode() {
            return code;
        }

    }

    public enum Frequency {
        //0-活动期间
        DURING_ACTIVITY(0),
        //1-每天
        BY_DAY(1);

        Frequency(int code) {
            this.code = code;
        }

        private int code;

        public int getCode() {
            return code;
        }
    }

    @Getter
    public enum ImportStatusEnum {

        NOT_IMPORTED(0, "未导入"),
        IMPORTING(1, "导入中"),
        IMPORT_SUCCESS(2, "导入成功");

        private Integer code;
        private String desc;

        ImportStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public boolean isEquals(String name) {
            return this.name().equals(name);
        }
    }

    @Getter
    public enum RewardStatusEnum {

        VALID_FAIL(-2, "customerId 校验不通过"),
        DELETE(-1, "记录被删除"),
        NOT_AWARD(0, "未发奖"),
        AWARDING(1, "发奖中"),
        AWARD_FAILED(2, "发奖失败"),
        AWARD_SUCCESS(3, "发奖成功");

        private Integer code;
        private String desc;

        RewardStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public boolean isEquals(String name) {
            return this.name().equals(name);
        }
    }

    @Getter
    public enum BatchRewardStatusEnum {

        NOT_IMPORTED(1, "未导入"),
        IMPORTING(2, "导入中"),
        IMPORT_FAILED(3, "导入失败"),
        UNAUDITED(4, "待审核"),
        REJECT(5, "审核不通过"),
        APPROVE(6, "审核通过"),
        AWARDING(7, "发奖中"),
        AWARD_FAILED(8, "发奖失败"),
        AWARD_SUCCESS(9, "发奖成功"),
        ODPS_RUNNING(10, "odps 执行中"),
        ODPS_FAILED(11, "odps 执行失败"),
        ODPS_COMPLETE(12, "odps 执行完成");

        private Integer code;
        private String desc;

        BatchRewardStatusEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public boolean isEquals(String name) {
            return this.name().equals(name);
        }
    }

    @Getter
    public enum AuditTypeEnum {
        APPROVE(0, "通过"),
        REJECT(1, "驳回");

        private Integer code;
        private String desc;

        AuditTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    @Getter
    public enum ActivityTypeEnum {
        HIERARCHY(0, "层级发奖"),
        INVITE(1, "邀请发奖"),
        CUSTOMIZE(2, "自定义发奖"),
        NORMAL(3, "常规发奖");

        private Integer code;
        private String desc;

        ActivityTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static ActivityTypeEnum value(String name) {
            try {
                return ActivityTypeEnum.valueOf(name);
            } catch (IllegalArgumentException ex) {
                return NORMAL;
            }
        }
    }

    public enum ActivitySubTypeEnum {
        NONE,   // 无类型
        TRADE_REBATE,   // 交易返佣
        KYC1;   // kyc1 奖励

        public static ActivitySubTypeEnum value(String subType) {
            if (StringUtils.isBlank(subType)) {
                return NONE;
            }
            return Arrays.stream(values()).filter(type -> StringUtils.equalsIgnoreCase(type.name(), subType)).findFirst().orElse(NONE);
        }
    }

    @Getter
    public enum LogKey {
        BUSINESS_NOTICE;

        public String format(String str) {
            return String.format("%s\n%s", this.name(), str);
        }
    }

    @Getter
    public enum SeqPrefix {

        CUSTOMIZE_SEQ("c", "c1", "自定义前缀"),
        INVITE_SEQ("i", "i1", "被邀请人邀请活动前缀"),
        HIERARCHY_SEQ("h", "h1", "多层级活动前缀"),
        NORMAL_SEQ("n", "n1", "普通活动前缀"),
        INVITEE_SEQ("e", "e1", "邀请人邀请活动前缀"),
        ;
        private String prefix;
        private String init;
        private String desc;

        SeqPrefix(String prefix, String init, String desc) {
            this.prefix = prefix;
            this.init = init;
            this.desc = desc;
        }

        public static SeqPrefix getSeq(String activityType) {
            switch (ActivityTypeEnum.value(activityType)) {
                case CUSTOMIZE:
                    return CUSTOMIZE_SEQ;
                case INVITE:
                    return INVITE_SEQ;
                case HIERARCHY:
                    return HIERARCHY_SEQ;
                default:
                    return NORMAL_SEQ;
            }
        }
    }


    @Getter
    public enum SideEnum {
        INVITER,
        INVITEE
    }

    @Getter
    public enum ValidStatus {

        VALID(0, "有效"),
        INVALID(1, "无效");

        private Integer status;
        private String desc;

        ValidStatus(Integer status, String desc) {
            this.status = status;
            this.desc = desc;
        }
    }

    @Getter
    public enum ActivityTaskStatus {

        ACTIVE(0),
        UNABLE(1);

        private int status;

        ActivityTaskStatus(int status) {
            this.status = status;
        }

        public static Integer getStatusByName(String name) {
            for (ActivityTaskStatus value : values()) {
                if (value.name().equalsIgnoreCase(name)) {
                    return value.status;
                }
            }
            return null;
        }
    }

    @Getter
    public enum ActivityMaterialStatus {
        EMPTY("empty", "物料为空"),
        ACTIVE("active", "上线"),
        UNABLE("unable", "下线");

        private String status;
        private String desc;

        ActivityMaterialStatus(String status, String desc) {
            this.status = status;
            this.desc = desc;
        }

        public boolean isEquals(String status) {
            return this.getStatus().equalsIgnoreCase(status);
        }
    }

    /**
     * 自动创建批次频率
     */
    @Getter
    public enum BatchCycleEnum {
        EVERY_DAY,//每天创建
        FINISH, //活动结束创建
        EVERY_WEEK, // 每周一
        EVERY_MONTH;
    }

    public enum AssignType {
        GENERAL,//普通红包
        FORTUNE; //拼手气红包

        public static AssignType getType(String type) {
            try {
                return AssignType.valueOf(type);
            } catch (Exception ex) {
                return FORTUNE;
            }
        }

        public boolean isEqual(String type) {
            return this.name().equals(type);
        }
    }

    /**
     * 发放的红包状态
     */
    @Getter
    public enum LuckFortuneReleaseStatus{

        INVALID("INVALID", "无效", -1),
        APPENDING("APPENDING", "创建中", 0),
        DRAWING("DRAWING", "进行中", 1),

        BUILDING("BUILDING","重构中", 2),
        DREW("DREW","领取完成", 3),

        EXPIRED("EXPIRED","过期", 6),

        REFUNDING("REFUNDING", "退款中",7),
        REFUND_FAIL("REFUND_FAIL","退款失败", 8),
        REFUND_SUCCESS("REFUND_SUCCESS","退款成功", 9)
        ;

        private String status;
        private String desc;
        private Integer code;

        LuckFortuneReleaseStatus(String status, String desc, Integer code){
            this.status = status;
            this.desc = desc;
            this.code = code;
        }

        public static LuckFortuneReleaseStatus valueOf(Integer code){
            return Arrays.stream(values()).filter(item -> item.getCode().equals(code)).findFirst().get();
        }
    }

    /**
     * 领取的红包状态
     */
    @Getter
    public enum LuckFortuneReceiveStatus{

        DRAWING("DRAWING", "领取中", 1),
        NOT_ACTIVATE("NOT_ACTIVATE", "待激活", 2),
        ACTIVATE_FAIL("ACTIVATE_FAIL","激活失败", 3),
        ACTIVATING("ACTIVATING", "激活中", 4),
        ACTIVATE_SUCCESS("ACTIVATE_SUCCESS","激活成功", 5),
        EXPIRED("EXPIRED","过期", 6),
        REFUNDING("REFUNDING", "退款中", 7),
        REFUND_FAIL("REFUND_FAIL","退款失败", 8),
        REFUND_SUCCESS("REFUND_SUCCESS","退款成功", 9),
        DRAW_FAIL("DRAW_FAIL","领取失败", 10)

        ;

        private String status;
        private String desc;
        private Integer code;

        LuckFortuneReceiveStatus(String status, String desc, Integer code){
            this.status = status;
            this.desc = desc;
            this.code = code;
        }
    }

    @Getter
    public enum ProLimitCode {
        AIRDROP_RELEASE_LIMIT, AIRDROP_RECEIVE_LIMIT;
    }

    @Getter
    public enum FirebaseTemplateCode {

        manual_reward("fcm.title.reward", "fcm.body.reward"),
        activity_invite_reward("fcm.title.activity.reward.invite", "fcm.body.activity.reward.invite"),
        activity_invite_reward_more("fcm.title.activity.reward.invite.more", "fcm.body.activity.reward.invite.more"),
        activity_reward("fcm.title.activity.reward", "fcm.body.activity.reward"),
        air_drop_alert("fcm.title.air.drop", "fcm.body.air.drop");

        private String titleKey;
        private String bodyKey;

        FirebaseTemplateCode(String titleKey, String bodyKey) {
            this.titleKey = titleKey;
            this.bodyKey = bodyKey;
        }
    }

    @Getter
    public enum EventCodeEnum {
        NONE("", "", "", 0),
        SIGN_IN("sign_in", "SIGN_IN", "签到", 1),
        FOLLOW("follow", "FOLLOW", "关注", 1),
        UNFOLLOW("follow_down", "FOLLOW", "取消关注", -1),
        LIKE("like", "LIKE", "赞", 1),
        REMOVE_LIKE("remove_like", "LIKE", "取消赞", -1),
        SHARE_POST("share_post", "SHARE_POST", "分享帖子", 1),
        SHARE_NEWS("share_news", "SHARE_NEWS", "分享资讯", 1),
        COMMENT("comment_up", "COMMENT", "评论", 1),
        REPLY("reply", "COMMENT", "回复", 1),
        POST("post", "POST", "发帖", 1),
        POST_SPLENDID("post_splendid", "POST_SPLENDID", "设置精品帖子",1),
        KYC("kyc", "KYC","kyc 认证", 1),
        KYC_L1("kyc_l1", "KYC_L1", "kycL1 认证", 1),
        KYC_L2("kyc_l2","KYC_L2" ,"kycL2 认证", 1),
        TRANSACTION_ANALYSIS("transaction_analysis", "TRANSACTION_ANALYSIS", "交易分析", 1),
        ASSET_ANALYSIS("asset_analysis", "ASSET_ANALYSIS", "资产分析", 1),
        ORDER_CREATED("order_created", "ORDER_CREATED","交易下单", 1),
        FOLLOWED("followed", "FOLLOWED", "被关注", 1),
        LIKED("liked", "LIKED", "被赞", 1),
        ;

        private String eventCode;
        private String eventMainCode;
        private String desc;
        private Integer inc;

        EventCodeEnum(String eventCode, String eventMainCode, String desc, Integer inc) {
            this.eventCode = eventCode;
            this.eventMainCode = eventMainCode;
            this.desc = desc;
            this.inc = inc;
        }

        static Map<String, EventCodeEnum> map = new HashMap<>();

        public static EventCodeEnum getEvent(String eventCode) {
            if (map.get(eventCode) == null) {
                Optional<EventCodeEnum> event = Arrays.stream(EventCodeEnum.values())
                        .filter(eventCodeEnum -> eventCodeEnum.getEventCode().equalsIgnoreCase(eventCode))
                        .findFirst();
                if (event.isPresent()) {
                    map.put(eventCode, event.get());
                    return event.get();
                }
            }
            return map.get(eventCode);
        }
    }

    @Getter
    public enum TaskTypeEnum {

        NORMAL(0, "normal"),
        ADVANCED(1, "advanced"),
        NOVICE(2, "novice"),
        ;

        private Integer level;
        private String type;

        TaskTypeEnum(Integer level, String type) {
            this.level = level;
            this.type = type;
        }

        public static TaskTypeEnum getByLevel(Integer level) {
            return Arrays.stream(values()).filter(item -> item.getLevel() == level).findFirst().orElse(ADVANCED);
        }
    }

    /**
     * 任务状态
     */
    public enum TaskStatusEnum {
        NOT_STARTED,
        APPENDING,
        DOING,//进行中
        DONE,//已完成
        FAIL;
    }

    public enum AwardTypeEnum {
        TOKEN,
        POINT,
        REWARD,
        Token,
        NFT,
        GAME_ITEM,
        BADGE,
        EXPERIENCE,
        SETS,
        TICKET,
        VOUCHER,
        BOOST,
        EXTRA_BOOST,
        AURA,
        EXPERIENCE_VOUCHER,
        OJO,
        EARNALLIANCE,
        EARNALLIANCE_CUSTOMER,
        SEASON_POINT
        ;

        public static AwardTypeEnum getType(String type){
            try{
                return AwardTypeEnum.valueOf(type.toUpperCase());
            }catch (Exception ex){
                return TOKEN;
            }
        }
    }

    public enum ActivitySourceEnum {
        TASK, OPERATE
    }

    /**
     * 库存状态
     */
    public enum StoreStatusEnum {
        NOT_ENOUGH, //不足
        ENOUGH, //充足
        LIMITLESS //无限
    }

    public enum LotteryProductCodeEnum {
        ACTIVITY_LOTTERY_LIMIT
    }

    @Getter
    public enum ActivityStatusEnum{

        DRAFT(0),
        /**
         * <code>ACTIVE = 1;</code>
         */
        ACTIVE(1),
        /**
         * <code>PAUSE = 2;</code>
         */
        PAUSE(2),
        /**
         * <code>END = 3;</code>
         */
        END(3);

        private Integer code;

        ActivityStatusEnum(Integer code){
            this.code = code;
        }

        public boolean isEquals(Integer code){
            return this.code.equals(code);
        }

        public static ActivityStatusEnum getStatus(Integer code){
            return Arrays.stream(values()).filter(status -> status.getCode().equals(code)).findFirst().get();
        }
    }

    public enum ActivityJoinStatus {
        APPENDING,
        DONE
    }

    public enum AirDropEventEnum{
        REVERT,//回归奖池
        EXPIRE,//过期
        REFUND//退款
    }

    // 奖励类型枚举
    public enum RewardBusinessType {
        lottery("lottery"),    // 抽奖
        reward("reward"),     // 奖励
        invite_kyc1("inviteKyc1"),    // kyc 奖励
        invite_trade_rebate("inviteTradeRebate"),    // 交易返佣奖励
        task("task"); // 这个不会出现在钱包历史中

        private String key;

        RewardBusinessType(String key) {
            this.key = key;
        }

        public String getKey() {
            return this.key;
        }

        public static ActivityConstant.RewardBusinessType get(String activityType, String activitySubType) {
            switch (ActivityConstant.ActivityTypeEnum.value(activityType)) {
                case INVITE:
                    switch (ActivityConstant.ActivitySubTypeEnum.value(activitySubType)) {
                        case NONE:
                            return ActivityConstant.RewardBusinessType.reward;
                        case KYC1:
                            return ActivityConstant.RewardBusinessType.invite_kyc1;
                        case TRADE_REBATE:
                            return ActivityConstant.RewardBusinessType.invite_trade_rebate;
                    }
                default:
                    return ActivityConstant.RewardBusinessType.reward;
            }
        }

        public static ActivityConstant.RewardBusinessType getByKey(String key) {
            return Arrays.stream(values()).filter(type -> StringUtils.equalsIgnoreCase(type.key, key)).findFirst().orElse(null);
        }
    }

    @Getter
    public enum CommonStatus {

        ACTIVE(0),
        DISABLE(1),

        //灰度
        GRAY(2)
        ;
        private int code;

        CommonStatus(int code){
            this.code = code;
        }
    }

    @Getter
    public enum GoodsStatus{
        ONLINE(0),//上架
        OFFLINE(1),//下架
        EXPIRE(2),//过期
        SOLD_OUT(3);//售罄

        private int code;

        GoodsStatus(int code){
            this.code = code;
        }
    }

    @Getter
    public enum BannerSource{
        TASK,
        GOODS;
    }

    @Getter
    public enum VipLevelEnum{
        NORMAL(0),
        L1(1)
        ;

        private Integer level;

        VipLevelEnum(Integer level){
            this.level = level;
        }
    }

    @Getter
    public enum TaskCompleteStatus {
        DONE(1),
        APPENDING(0);

        private int status;

        TaskCompleteStatus(int status){
            this.status = status;
        }
    }

    @Getter
    public enum TaskStatusCondition {
        twitter_auth, task
    }

    @Getter
    public enum LotteryStatus {
        NO_PAID ,APPENDING, FAIL, SUCCESS, CANCEL
    }


    @Getter
    public enum CouponBusinessTypeEnum {
        mugen_kol_redeem
    }

    @Getter
    public enum BadgeTypeEnum {
        BADGE_ID,
        BADGE_CODE
    }

    @Getter
    public enum OspNftCallbackTypeEnum {
        OWNER_NFT_CALLBACK,
        CUSTOMER_NFT_CALLBACK
    }

    @Getter
    public enum SeasonTimeType{
        //周期不固定
        period_not_fixed,
        monthly_period_fixed,
    }

    public enum EntitlementStatusEnum {
        UNCLAIMED,
        CLAIMED,
        EXPIRED,
        LOCKED
    }

    public enum RewardTypeEnum {
        PROGRESS_CHEST,
        GRANTED_PACK
    }
}
